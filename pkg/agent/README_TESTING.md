# Testing Guide for Agent Package

This document explains how to run different types of tests in the agent package.

## Test Organization

The tests are organized into two categories:

### 1. Unit Tests (Fast)
- **Files**: `*_test.go` (excluding `*_performance_test.go`)
- **Purpose**: Test individual functions and methods with mocks
- **Duration**: Fast execution (< 2 seconds)
- **Coverage**: Core functionality, error handling, edge cases

### 2. Performance Tests (Slow)
- **Files**: `*_performance_test.go`
- **Purpose**: Test system limits, concurrent operations, and performance characteristics
- **Duration**: Slow execution (can take minutes)
- **Coverage**: Connection limits, throughput, concurrent operations

## Running Tests

### Run Only Unit Tests (Recommended for Development)
```bash
# Run all unit tests quickly (skips performance tests)
go test ./pkg/agent -v -short

# Run specific unit test
go test ./pkg/agent -v -short -run TestNewClient

# Run all client-related unit tests
go test ./pkg/agent -v -short -run "TestClient.*"
```

### Run Performance Tests
```bash
# Run all performance tests (takes time)
go test ./pkg/agent -v -run ".*Performance.*|.*ConnectionLimits.*|.*Progressive.*"

# Run specific performance test
go test ./pkg/agent -v -run TestConcurrentConnectionLimits

# Run with timeout for long-running tests
go test ./pkg/agent -v -timeout 5m -run TestConcurrentConnectionLimits
```

### Run All Tests
```bash
# Run everything (unit + performance tests)
go test ./pkg/agent -v

# Run with coverage
go test ./pkg/agent -v -cover
```

## Test Categories

### Unit Tests Include:
- **Client Tests** (`client_test.go`):
  - `TestNewClient` - Constructor and configuration
  - `TestClientRun` - Main run loop and lifecycle
  - `TestClientProcessIncoming` - Incoming packet processing
  - `TestClientProcessOutgoing` - Outgoing packet processing
  - `TestClientServe` - Stream management
  - `TestClientConfig` - Configuration validation
  - `TestClientIntegration` - End-to-end integration
  - `TestClientErrorScenarios` - Error handling

- **ProxyManager Tests** (`proxymanager_test.go`):
  - Basic functionality tests
  - Connection management
  - Packet dispatching
  - Error handling
  - Resource cleanup

### Performance Tests Include:
- **Connection Limits** (`proxymanager_performance_test.go`):
  - `TestConcurrentConnectionLimits` - Tests with 100-10000 connections
  - `TestConnectionLimitProgressive` - Binary search for connection limits
  - `TestConcurrentDispatchPerformance` - High-concurrency packet processing

- **Benchmarks**:
  - `BenchmarkDispatchNewConnection` - New connection creation performance
  - `BenchmarkDispatchExistingConnection` - Existing connection performance

## Performance Test Details

### TestConcurrentConnectionLimits
- Tests system with increasing connection counts
- Validates success rates and error handling
- Helps identify system limits

### TestConnectionLimitProgressive
- Uses binary search to find exact connection limits
- Skipped in short mode (`-short` flag)
- Useful for capacity planning

### TestConcurrentDispatchPerformance
- Tests concurrent packet processing
- Measures throughput and latency
- Validates packet ordering for same connections

## Best Practices

### For Development
1. **Always run unit tests first**: `go test ./pkg/agent -v -short`
2. **Run specific tests when debugging**: `go test ./pkg/agent -v -short -run TestSpecificFunction`
3. **Use performance tests for capacity planning**: Run occasionally to understand limits

### For CI/CD
1. **Unit tests in every build**: Fast feedback loop
2. **Performance tests in nightly builds**: Catch performance regressions
3. **Set appropriate timeouts**: Performance tests can take several minutes

### For Debugging
1. **Use verbose output**: `-v` flag shows detailed test output
2. **Run single tests**: `-run TestName` to focus on specific issues
3. **Check logs**: Tests include detailed logging for debugging

## Mock Infrastructure

The tests use comprehensive mocks:
- `mockClientProxyManager` - Simulates proxy manager behavior
- `mockClientTargetResolver` - Simulates target resolution
- `mockTunnelClient` - Simulates gRPC tunnel client
- `mockServer` - Provides real TCP server for integration tests

These mocks enable testing complex scenarios without external dependencies.

## Test Coverage

Current test coverage includes:
- ✅ Constructor and configuration validation
- ✅ Connection lifecycle management
- ✅ Packet processing (incoming/outgoing)
- ✅ Error handling and recovery
- ✅ Concurrent operations
- ✅ Resource cleanup
- ✅ Performance characteristics
- ✅ System limits and capacity

## Troubleshooting

### Common Issues
1. **Tests timeout**: Increase timeout with `-timeout 10m`
2. **Port conflicts**: Tests use dynamic ports, but conflicts can occur
3. **Resource limits**: Performance tests may hit OS limits (file descriptors, etc.)

### Performance Test Failures
- **Connection limits**: May vary by system configuration
- **Timing issues**: Performance tests are sensitive to system load
- **Resource exhaustion**: Monitor system resources during tests

## Contributing

When adding new tests:
1. **Unit tests** go in `*_test.go` files
2. **Performance tests** go in `*_performance_test.go` files
3. **Use `testing.Short()`** to skip slow tests in short mode
4. **Add comprehensive mocks** for external dependencies
5. **Include both success and failure scenarios**
